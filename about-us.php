<?php 
include 'includes/header.php';
?>

<!-- About Us Hero Section -->
<section class="about-hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">Committed to Your Health Journey</h1>
            <p class="hero-subtitle">Where Wellness Begins, Naturally</p>
        </div>
    </div>
</section>

<!-- Main About Section -->
<section class="about-main">
    <div class="container">
        <div class="about-content">
            <div class="about-text">
                <p class="intro-text">
                    At Alpha Nutrition, we believe good health starts with nature. That's why we offer a carefully crafted 
                    range of vitamins, minerals, herbal supplements, and wellness blends—all made with pure ingredients 
                    and backed by trust.
                </p>
                <p class="description-text">
                    Whether you're looking to boost your energy, support your immunity, improve digestion, or take 
                    care of your overall well-being, Alpha Nutrition is here to support your daily health—naturally.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="why-choose-us">
    <div class="container">
        <h2 class="section-title">Why Choose Alpha Nutrition?</h2>
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-leaf"></i>
                </div>
                <h3 class="feature-title">Natural Ingredients</h3>
                <p class="feature-description">
                    We use plant-based, clean, and nutrient-rich ingredients sourced from nature.
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-microscope"></i>
                </div>
                <h3 class="feature-title">Backed by Science</h3>
                <p class="feature-description">
                    Our formulations are developed with care, combining traditional knowledge with modern research.
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="feature-title">Quality You Can Trust</h3>
                <p class="feature-description">
                    Every product is manufactured in GMP-certified facilities with strict quality checks.
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="feature-title">Wellness for Everyone</h3>
                <p class="feature-description">
                    From daily essentials to specific health goals, we have products for every age and lifestyle.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Product Range Section -->
<section class="product-range">
    <div class="container">
        <h2 class="section-title">Explore Our Product Range</h2>
        <div class="product-categories">
            <div class="category-item">
                <div class="category-icon">
                    <i class="fas fa-pills"></i>
                </div>
                <div class="category-content">
                    <h3 class="category-title">Daily Multivitamins</h3>
                    <p class="category-description">Gentle on the stomach, rich in nutrients</p>
                </div>
            </div>
            
            <div class="category-item">
                <div class="category-icon">
                    <i class="fas fa-seedling"></i>
                </div>
                <div class="category-content">
                    <h3 class="category-title">Herbal Supplements</h3>
                    <p class="category-description">Like Ashwagandha, Giloy, Turmeric & more</p>
                </div>
            </div>
            
            <div class="category-item">
                <div class="category-icon">
                    <i class="fas fa-shield-virus"></i>
                </div>
                <div class="category-content">
                    <h3 class="category-title">Immunity Support</h3>
                    <p class="category-description">Natural defense blends for all seasons</p>
                </div>
            </div>
            
            <div class="category-item">
                <div class="category-icon">
                    <i class="fas fa-venus-mars"></i>
                </div>
                <div class="category-content">
                    <h3 class="category-title">Men's & Women's Health</h3>
                    <p class="category-description">Balance, strength & hormonal wellness</p>
                </div>
            </div>
            
            <div class="category-item">
                <div class="category-icon">
                    <i class="fas fa-stomach"></i>
                </div>
                <div class="category-content">
                    <h3 class="category-title">Digestion & Detox</h3>
                    <p class="category-description">Cleanse and support your gut naturally</p>
                </div>
            </div>
            
            <div class="category-item">
                <div class="category-icon">
                    <i class="fas fa-heartbeat"></i>
                </div>
                <div class="category-content">
                    <h3 class="category-title">Joint & Heart Health</h3>
                    <p class="category-description">Stay active, flexible & heart-healthy</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Product Highlights Section -->
<section class="product-highlights">
    <div class="container">
        <div class="highlights-content">
            <div class="highlight-card">
                <div class="highlight-header">
                    <div class="highlight-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="highlight-title">Product Highlights</h3>
                </div>

                <div class="highlight-description">
                    <p>High-strength Omega 3 fish oil from wild-caught salmon with essential EPA & DHA to support heart, joint, brain, and skin health. Non-enteric coated for better absorption and certified mercury-free.</p>
                </div>

                <div class="highlight-features">
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Wild-caught salmon source</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Essential EPA & DHA</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Mercury-free certified</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Better absorption formula</span>
                    </div>
                </div>

                <div class="highlight-benefits">
                    <h4>Health Benefits:</h4>
                    <div class="benefits-grid">
                        <div class="benefit-item">
                            <i class="fas fa-heart"></i>
                            <span>Heart Health</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-bone"></i>
                            <span>Joint Support</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-brain"></i>
                            <span>Brain Function</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-eye"></i>
                            <span>Skin Health</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="about-cta">
    <div class="container">
        <div class="cta-content">
            <h2 class="cta-title">Ready to Start Your Wellness Journey?</h2>
            <p class="cta-description">Discover our range of natural health products designed to support your well-being.</p>
            <a href="products.php" class="cta-button">Shop Now</a>
        </div>
    </div>
</section>

<style>
/* About Us Page Styles */
.about-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6rem 0 4rem;
    text-align: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    font-weight: 300;
    opacity: 0.9;
}

.about-main {
    padding: 4rem 0;
    background: #f8f9fa;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.intro-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #333;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.description-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #666;
}

.why-choose-us {
    padding: 4rem 0;
    background: white;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 12px;
    background: #f8f9fa;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.feature-description {
    font-size: 1rem;
    line-height: 1.6;
    color: #666;
}

.product-range {
    padding: 4rem 0;
    background: #f8f9fa;
}

.product-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.category-item {
    display: flex;
    align-items: center;
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.category-item:hover {
    transform: translateY(-3px);
}

.category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.category-icon i {
    font-size: 1.5rem;
    color: white;
}

.category-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.category-description {
    font-size: 0.95rem;
    color: #666;
    line-height: 1.5;
}

.about-cta {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.cta-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    display: inline-block;
    background: #ff6b35;
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background: #e55a2b;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

/* Product Highlights Section - Black & White Design */
.product-highlights {
    padding: 4rem 0;
    background: #f8f9fa;
}

.highlights-content {
    max-width: 800px;
    margin: 0 auto;
}

.highlight-card {
    background: white;
    border: 2px solid #000;
    border-radius: 0;
    padding: 3rem;
    box-shadow: 8px 8px 0px #000;
    transition: all 0.3s ease;
}

.highlight-card:hover {
    transform: translate(-4px, -4px);
    box-shadow: 12px 12px 0px #000;
}

.highlight-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #000;
}

.highlight-icon {
    width: 50px;
    height: 50px;
    background: #000;
    color: white;
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.highlight-title {
    font-size: 1.8rem;
    font-weight: 800;
    color: #000;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.highlight-description {
    margin-bottom: 2rem;
}

.highlight-description p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #333;
    margin: 0;
    font-weight: 500;
}

.highlight-features {
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
}

.feature-item i {
    width: 20px;
    height: 20px;
    background: #000;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.feature-item span {
    font-weight: 600;
    color: #000;
    font-size: 1rem;
}

.highlight-benefits {
    border-top: 2px solid #000;
    padding-top: 1.5rem;
}

.highlight-benefits h4 {
    font-size: 1.3rem;
    font-weight: 800;
    color: #000;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.benefit-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
    border: 2px solid #000;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    background: #000;
    color: white;
}

.benefit-item i {
    font-size: 1.5rem;
    color: #000;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.benefit-item:hover i {
    color: white;
}

.benefit-item span {
    font-weight: 700;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.2rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .product-categories {
        grid-template-columns: 1fr;
    }
    
    .category-item {
        flex-direction: column;
        text-align: center;
    }
    
    .category-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    /* Product Highlights Responsive */
    .highlight-card {
        padding: 2rem;
        box-shadow: 4px 4px 0px #000;
    }

    .highlight-card:hover {
        transform: translate(-2px, -2px);
        box-shadow: 6px 6px 0px #000;
    }

    .highlight-header {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .highlight-title {
        font-size: 1.5rem;
    }

    .benefits-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .benefit-item {
        padding: 0.75rem;
    }
}

@media (max-width: 480px) {
    .highlight-card {
        padding: 1.5rem;
        margin: 0 1rem;
    }

    .highlight-title {
        font-size: 1.3rem;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
